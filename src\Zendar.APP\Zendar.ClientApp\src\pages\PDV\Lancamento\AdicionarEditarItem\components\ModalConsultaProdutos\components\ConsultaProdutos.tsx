import { Flex, Text, Button, Icon, Tr, Td, Box } from '@chakra-ui/react';
import { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';

import auth from 'modules/auth';

import { moneyMask } from 'helpers/format/fieldsMasks';

import {
  Produto,
  useConsultaProdutosPdvContext,
} from 'store/PDV/ConsultaProdutoPdv';

import { PagedTable } from 'components/update/Table/PagedTable';

import PlanoContratacaoEnum from 'constants/enum/planoContratacao';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import { PesquisarEstoqueIcon } from 'icons';

import { EnumTelaConsultaProdutosPdv } from '..';

interface ConsultaProdutoProps {
  tela: EnumTelaConsultaProdutosPdv;
  casasDecimais: { casasDecimaisQuantidade: number };
  produtoSelecionado: Produto | null;
  exibirBotaoAdicionarProduto: boolean;
  isOpenDrawer: boolean;
  toggleSelect: (produto: Produto) => void;
  lancarProdutoSimples: (produto: Produto) => void;
  setProdutoSelecionado: (produto: Produto | null) => void;
  setModalVisualizarEstoqueEstaAberto: (aberto: boolean) => void;
  escolherVariacao: (id: string) => void;
  onCloseDrawer: () => void;
}

export const ConsultaProdutos = ({
  tela,
  casasDecimais,
  produtoSelecionado,
  exibirBotaoAdicionarProduto,
  isOpenDrawer,
  setProdutoSelecionado,
  setModalVisualizarEstoqueEstaAberto,
  escolherVariacao,
  toggleSelect,
  lancarProdutoSimples,
  onCloseDrawer,
}: ConsultaProdutoProps) => {
  const { paginationHandle, produtos, totalRegistros } =
    useConsultaProdutosPdvContext();

  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const [tabelaEstaFocada, setTabelaEstaFocada] = useState<boolean>(false);
  const containerTabelaRef = useRef<HTMLDivElement>(null);

  const scrollToFocusedItem = useCallback((index: number) => {
    const containerTabela = containerTabelaRef.current;
    if (!containerTabela) return;

    const linha = containerTabela.querySelectorAll('tbody tr');
    const linhaFocada = linha[index] as HTMLElement;

    if (linhaFocada) {
      const containerRect = containerTabela.getBoundingClientRect();
      const linhaRect = linhaFocada.getBoundingClientRect();

      const cabecalhoTabela = containerTabela.querySelector('thead');
      const tamanhoCabecalho = cabecalhoTabela
        ? cabecalhoTabela.getBoundingClientRect().height
        : 0;

      const topoTabelaVisivel = containerRect.top + tamanhoCabecalho;
      const fimTabelaVisivel = containerRect.bottom;

      const estaAcimaDaTabelaVisivel = linhaRect.top < topoTabelaVisivel;
      const estaAbaixoDaTabelaVisivel = linhaRect.bottom > fimTabelaVisivel;

      if (estaAcimaDaTabelaVisivel || estaAbaixoDaTabelaVisivel) {
        if (index === 0) {
          containerTabela.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        } else if (index === linha.length - 1) {
          containerTabela.scrollTo({
            top: containerTabela.scrollHeight - containerTabela.clientHeight,
            behavior: 'smooth',
          });
        } else {
          linhaFocada.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    }
  }, []);

  const cabecalhoHeader = () => {
    return [
      {
        content: 'Nome',
        key: 'Nome',
        isOrderable: true,
        width: 'auto',
        pl: totalRegistros > 0 ? '72px !important' : '40px !important',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Preço de venda
          </Flex>
        ),
        key: 'PrecoVenda',
        isOrderable: false,
        width: '150px',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Estoque
          </Flex>
        ),
        key: 'Estoque',
        isOrderable: false,
        width: ['120px', '120px', '200px'],
      },
      {
        content: '',
        key: '',
        isOrderable: false,
        width: '80px',
      },
    ];
  };

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      const activeElement = document.activeElement;
      const isInputFocused =
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'SELECT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.getAttribute('role') === 'combobox';

      if (
        isOpenDrawer ||
        !tabelaEstaFocada ||
        isInputFocused ||
        !produtos?.length
      ) {
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = Math.min(prev + 1, produtos.length - 1);
            scrollToFocusedItem(newIndex);
            return newIndex;
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = Math.max(prev - 1, 0);
            scrollToFocusedItem(newIndex);
            return newIndex;
          });
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && produtos[focusedIndex]) {
            const produto = produtos[focusedIndex];
            const produtoEstaSelecionado =
              produtoSelecionado?.id === produto.id;

            if (e.shiftKey) {
              if (produto.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO) {
                setProdutoSelecionado(produto);
                escolherVariacao(produto.id);
              } else if (exibirBotaoAdicionarProduto) {
                lancarProdutoSimples(produto);
              }
            } else {
              toggleSelect(produto);

              if (produtoEstaSelecionado) {
                if (produto.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO) {
                  setProdutoSelecionado(produto);
                  escolherVariacao(produto.id);
                } else if (exibirBotaoAdicionarProduto) {
                  lancarProdutoSimples(produto);
                }
              }
            }
          }
          break;
      }
    },
    [
      isOpenDrawer,
      tabelaEstaFocada,
      produtos,
      focusedIndex,
      toggleSelect,
      produtoSelecionado,
      setProdutoSelecionado,
      escolherVariacao,
      exibirBotaoAdicionarProduto,
      lancarProdutoSimples,
      scrollToFocusedItem,
    ]
  );

  const planoContratado = useMemo(() => auth.getPlano(), []);

  const planosNaoPossuemLocalEstoque = [
    PlanoContratacaoEnum.BASIC,
    PlanoContratacaoEnum.START,
    PlanoContratacaoEnum.PRO,
  ];

  const planoNaoPossuiLocalEstoque =
    planosNaoPossuemLocalEstoque.includes(planoContratado);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    if (produtos?.length > 0) {
      setFocusedIndex((prev) => {
        if (prev < 0 || prev >= produtos.length) {
          return 0;
        }
        return prev;
      });
      setTabelaEstaFocada(true);
      setTimeout(() => {
        containerTabelaRef.current?.focus();
      }, 100);
    } else {
      setFocusedIndex(-1);
    }
  }, [produtos]);

  useEffect(() => {
    if (focusedIndex >= 0 && produtos?.length > 0) {
      setTimeout(() => {
        scrollToFocusedItem(focusedIndex);
      }, 50);
    }
  }, [focusedIndex, scrollToFocusedItem, produtos]);

  useEffect(() => {
    if (produtoSelecionado && produtos?.length > 0) {
      const index = produtos.findIndex((p) => p.id === produtoSelecionado.id);
      if (index >= 0 && index !== focusedIndex) {
        setFocusedIndex(index);
      }
    }
  }, [produtoSelecionado, produtos, focusedIndex]);

  const handleTableClick = useCallback((e: React.MouseEvent) => {
    // Só foca se o clique não foi em um botão ou elemento interativo
    const target = e.target as HTMLElement;
    const isInteractiveElement =
      target.tagName === 'BUTTON' ||
      target.closest('button') ||
      target.getAttribute('role') === 'button';

    if (!isInteractiveElement) {
      setTabelaEstaFocada(true);
      setTimeout(() => {
        containerTabelaRef.current?.focus();
      }, 10);
    }
  }, []);

  const handleTableBlur = useCallback(() => {
    setTabelaEstaFocada(false);
  }, []);

  if (tela !== EnumTelaConsultaProdutosPdv.PRODUTOS) {
    return null;
  }

  return (
    <Box
      ref={containerTabelaRef}
      tabIndex={0}
      onClick={handleTableClick}
      onBlur={handleTableBlur}
      onFocus={() => setTabelaEstaFocada(true)}
      outline="none"
      _focus={{ outline: 'none' }}
    >
      <PagedTable
        loadColumnsData={paginationHandle}
        itemsTotalCount={totalRegistros}
        defaultKeyOrdered="Nome"
        itensPerPage={25}
        overflowY="auto"
        maxH="calc(100vh - 150px)"
        tableHeaders={cabecalhoHeader()}
        renderTableRows={produtos?.map((produto, index) => {
          const hasEstoque = produto.estoque > 0;
          const isFocused = focusedIndex === index;
          const isSelected = produtoSelecionado?.id === produto.id;

          return (
            <Tr
              key={produto.id}
              sx={{
                '& td': {
                  color: hasEstoque ? 'gray.700' : 'red.500',
                },
              }}
              height="52px"
              bg={
                isFocused ? 'gray.50' : isSelected ? 'purple.50' : 'transparent'
              }
              borderLeft={isFocused ? '3px solid' : 'none'}
              borderLeftColor={isFocused ? 'gray.400' : 'transparent'}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setFocusedIndex(index);
                toggleSelect(produto);
                // Retorna o foco para o container da tabela para permitir navegação por teclado
                setTimeout(() => {
                  containerTabelaRef.current?.focus();
                  setTabelaEstaFocada(true);
                }, 10);
              }}
              onDoubleClick={() => {
                if (produto) {
                  setFocusedIndex(index);
                  if (
                    produto.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO
                  ) {
                    setProdutoSelecionado(produto);
                    escolherVariacao(produto.id);
                    return;
                  }
                  if (exibirBotaoAdicionarProduto) {
                    lancarProdutoSimples(produto);
                    // Retorna o foco após adicionar produto
                    setTimeout(() => {
                      containerTabelaRef.current?.focus();
                      setTabelaEstaFocada(true);
                    }, 10);
                  }
                }
              }}
              cursor="pointer"
            >
              <Td>
                <Text
                  display="flex"
                  alignItems="center"
                  gap="12px"
                  justifyContent="flex-start"
                  pl={isSelected ? '0px' : '32px'}
                >
                  {isSelected && (
                    <IoIosCheckmarkCircleOutline
                      color="#482ABC"
                      fontSize="20px"
                    />
                  )}
                  {produto.nome}
                  {produto.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO && (
                    <Button
                      colorScheme="teal"
                      variant="solid"
                      height="16px"
                      fontWeight="500"
                      fontSize="12px"
                      ml="8px"
                      px="8px"
                      py="10px"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setFocusedIndex(index);
                        setProdutoSelecionado(produto);
                        escolherVariacao(produto.id);
                      }}
                      onDoubleClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setFocusedIndex(index);
                        setProdutoSelecionado(produto);
                        escolherVariacao(produto.id);
                      }}
                    >
                      Ver variações
                    </Button>
                  )}
                </Text>
              </Td>
              <Td isNumeric>
                <Flex justifyContent="flex-end">
                  {moneyMask(produto.precoVenda, true)}
                </Flex>
              </Td>
              <Td
                textAlign="end"
                color={produto.estoque <= 0 ? 'red.400' : undefined}
              >
                <Flex alignItems="center" justify="flex-end" gap="24px">
                  <Text>
                    {produto.estoque.toLocaleString('locale', {
                      minimumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                      maximumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                    })}
                  </Text>
                </Flex>
              </Td>
              {planoNaoPossuiLocalEstoque ? (
                <Td />
              ) : (
                <Td pt="0" pb="0">
                  <Icon
                    color={produto.estoque <= 0 ? 'red.400' : 'primary.50'}
                    cursor="pointer"
                    as={PesquisarEstoqueIcon}
                    fontSize="lg"
                    onClick={() => {
                      if (isOpenDrawer) onCloseDrawer();
                      setModalVisualizarEstoqueEstaAberto(true);
                      setProdutoSelecionado(produto);
                    }}
                  />
                </Td>
              )}
            </Tr>
          );
        })}
      />
    </Box>
  );
};
